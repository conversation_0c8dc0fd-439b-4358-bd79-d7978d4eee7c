-- AlterTable
ALTER TABLE "feedbacks" ADD COLUMN     "department_id" INTEGER,
ADD COLUMN     "organization_id" INTEGER,
ADD COLUMN     "task_id" INTEGER;

-- CreateIndex
CREATE INDEX "feedbacks_task_id_idx" ON "feedbacks"("task_id");

-- CreateIndex
CREATE INDEX "feedbacks_department_id_idx" ON "feedbacks"("department_id");

-- CreateIndex
CREATE INDEX "feedbacks_organization_id_idx" ON "feedbacks"("organization_id");

-- AddForeignKey
ALTER TABLE "feedbacks" ADD CONSTRAINT "feedbacks_task_id_fkey" FOREIGN KEY ("task_id") REFERENCES "tasks"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "feedbacks" ADD CONSTRAINT "feedbacks_department_id_fkey" FOREIGN KEY ("department_id") REFERENCES "departments"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "feedbacks" ADD CONSTRAINT "feedbacks_organization_id_fkey" FOREIGN KEY ("organization_id") REFERENCES "organizations"("id") ON DELETE SET NULL ON UPDATE CASCADE;
