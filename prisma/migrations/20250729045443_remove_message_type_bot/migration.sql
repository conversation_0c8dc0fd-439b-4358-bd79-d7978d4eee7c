/*
  Warnings:

  - The values [bot] on the enum `message_types` will be removed. If these variants are still used in the database, this will fail.

*/
-- AlterEnum
BEGIN;
CREATE TYPE "message_types_new" AS ENUM ('text', 'image', 'file', 'sticker', 'link');
ALTER TABLE "chat_messages" ALTER COLUMN "message_type" DROP DEFAULT;
ALTER TABLE "chat_messages" ALTER COLUMN "message_type" TYPE "message_types_new" USING ("message_type"::text::"message_types_new");
ALTER TYPE "message_types" RENAME TO "message_types_old";
ALTER TYPE "message_types_new" RENAME TO "message_types";
DROP TYPE "message_types_old";
ALTER TABLE "chat_messages" ALTER COLUMN "message_type" SET DEFAULT 'text';
COMMIT;
