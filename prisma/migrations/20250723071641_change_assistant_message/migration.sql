/*
  Warnings:

  - You are about to drop the column `message_type_id` on the `assistant_messages` table. All the data in the column will be lost.
  - Added the required column `assistant_message_type_id` to the `assistant_messages` table without a default value. This is not possible if the table is not empty.

*/
-- DropForeignKey
ALTER TABLE "assistant_messages" DROP CONSTRAINT "assistant_messages_message_type_id_fkey";

-- DropIndex
DROP INDEX "assistant_messages_message_type_id_idx";

-- AlterTable
ALTER TABLE "assistant_messages" DROP COLUMN "message_type_id",
ADD COLUMN     "assistant_message_type_id" INTEGER NOT NULL;

-- CreateIndex
CREATE INDEX "assistant_messages_assistant_message_type_id_idx" ON "assistant_messages"("assistant_message_type_id");

-- AddForeignKey
ALTER TABLE "assistant_messages" ADD CONSTRAINT "assistant_messages_assistant_message_type_id_fkey" FOREIGN KEY ("assistant_message_type_id") REFERENCES "assistant_message_types"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
