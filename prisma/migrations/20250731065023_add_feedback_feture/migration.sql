-- CreateTable
CREATE TABLE "feedback_types" (
    "id" SERIAL NOT NULL,
    "name" TEXT NOT NULL,
    "description" TEXT,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "feedback_types_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "feedbacks" (
    "id" SERIAL NOT NULL,
    "type_id" INTEGER NOT NULL,
    "create_from_id" INTEGER NOT NULL,
    "situation" TEXT,
    "behavior" TEXT,
    "impact" TEXT,
    "actionable" TEXT,
    "appreciation" TEXT,
    "growth_token" TEXT,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "feedbacks_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "feedback_users" (
    "id" SERIAL NOT NULL,
    "feedback_id" INTEGER NOT NULL,
    "user_id" INTEGER NOT NULL,
    "is_accept" BOOLEAN,
    "is_discard" BOOLEAN,
    "reflection" TEXT,
    "is_share" BOOLEAN NOT NULL DEFAULT false,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "feedback_users_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "feedback_types_name_key" ON "feedback_types"("name");

-- CreateIndex
CREATE INDEX "feedbacks_type_id_idx" ON "feedbacks"("type_id");

-- CreateIndex
CREATE INDEX "feedbacks_create_from_id_idx" ON "feedbacks"("create_from_id");

-- CreateIndex
CREATE INDEX "feedbacks_created_at_idx" ON "feedbacks"("created_at");

-- CreateIndex
CREATE INDEX "feedback_users_user_id_idx" ON "feedback_users"("user_id");

-- CreateIndex
CREATE INDEX "feedback_users_feedback_id_idx" ON "feedback_users"("feedback_id");

-- CreateIndex
CREATE INDEX "feedback_users_is_accept_idx" ON "feedback_users"("is_accept");

-- CreateIndex
CREATE INDEX "feedback_users_is_discard_idx" ON "feedback_users"("is_discard");

-- CreateIndex
CREATE INDEX "feedback_users_is_share_idx" ON "feedback_users"("is_share");

-- CreateIndex
CREATE UNIQUE INDEX "feedback_users_feedback_id_user_id_key" ON "feedback_users"("feedback_id", "user_id");

-- AddForeignKey
ALTER TABLE "feedbacks" ADD CONSTRAINT "feedbacks_type_id_fkey" FOREIGN KEY ("type_id") REFERENCES "feedback_types"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "feedbacks" ADD CONSTRAINT "feedbacks_create_from_id_fkey" FOREIGN KEY ("create_from_id") REFERENCES "users"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "feedback_users" ADD CONSTRAINT "feedback_users_feedback_id_fkey" FOREIGN KEY ("feedback_id") REFERENCES "feedbacks"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "feedback_users" ADD CONSTRAINT "feedback_users_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "users"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
