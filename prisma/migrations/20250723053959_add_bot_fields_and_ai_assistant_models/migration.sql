-- DropIndex
DROP INDEX "chats_last_message_at_idx";

-- AlterTable
ALTER TABLE "chats" ADD COLUMN     "bot_duration" INTEGER,
ADD COLUMN     "is_bot" BOOLEAN NOT NULL DEFAULT false;

-- CreateTable
CREATE TABLE "assistant_message_types" (
    "id" SERIAL NOT NULL,
    "name" TEXT NOT NULL,
    "description" TEXT,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "assistant_message_types_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "assistant_chat_users" (
    "id" SERIAL NOT NULL,
    "name" TEXT,
    "chat_id" INTEGER NOT NULL,
    "user_id" INTEGER NOT NULL,
    "is_active" BOOLEAN NOT NULL DEFAULT true,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,
    "last_interaction_at" TIMESTAMP(3),

    CONSTRAINT "assistant_chat_users_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "assistant_messages" (
    "id" SERIAL NOT NULL,
    "assistant_chat_user_id" INTEGER NOT NULL,
    "message_type_id" INTEGER NOT NULL,
    "content" TEXT NOT NULL,
    "metadata" JSONB,
    "completion_tokens" INTEGER,
    "prompt_tokens" INTEGER,
    "total_tokens" INTEGER,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "assistant_messages_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "assistant_message_types_name_key" ON "assistant_message_types"("name");

-- CreateIndex
CREATE INDEX "assistant_chat_users_user_id_is_active_idx" ON "assistant_chat_users"("user_id", "is_active");

-- CreateIndex
CREATE INDEX "assistant_chat_users_chat_id_is_active_idx" ON "assistant_chat_users"("chat_id", "is_active");

-- CreateIndex
CREATE INDEX "assistant_chat_users_last_interaction_at_idx" ON "assistant_chat_users"("last_interaction_at");

-- CreateIndex
CREATE UNIQUE INDEX "assistant_chat_users_chat_id_user_id_key" ON "assistant_chat_users"("chat_id", "user_id");

-- CreateIndex
CREATE INDEX "assistant_messages_assistant_chat_user_id_created_at_idx" ON "assistant_messages"("assistant_chat_user_id", "created_at");

-- CreateIndex
CREATE INDEX "assistant_messages_created_at_idx" ON "assistant_messages"("created_at");

-- CreateIndex
CREATE INDEX "assistant_messages_message_type_id_idx" ON "assistant_messages"("message_type_id");

-- CreateIndex
CREATE INDEX "assistant_messages_total_tokens_idx" ON "assistant_messages"("total_tokens");

-- AddForeignKey
ALTER TABLE "assistant_chat_users" ADD CONSTRAINT "assistant_chat_users_chat_id_fkey" FOREIGN KEY ("chat_id") REFERENCES "chats"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "assistant_chat_users" ADD CONSTRAINT "assistant_chat_users_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "users"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "assistant_messages" ADD CONSTRAINT "assistant_messages_assistant_chat_user_id_fkey" FOREIGN KEY ("assistant_chat_user_id") REFERENCES "assistant_chat_users"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "assistant_messages" ADD CONSTRAINT "assistant_messages_message_type_id_fkey" FOREIGN KEY ("message_type_id") REFERENCES "assistant_message_types"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
