import { prisma } from '@/lib/prisma';

/**
 * Check if a user is an owner of an organization
 */
export async function isOrganizationOwner(userId: number, organizationId: number): Promise<boolean> {
  try {
    const organization = await prisma.organization.findUnique({
      where: { id: organizationId },
      select: { ownerUserId: true },
    });

    return organization?.ownerUserId === userId;
  } catch (error) {
    console.error('Error checking organization ownership:', error);
    return false;
  }
}

/**
 * Check if a user is an admin of an organization
 */
export async function isOrganizationAdmin(userId: number, organizationId: number): Promise<boolean> {
  try {
    const admin = await prisma.organizationAdmin.findUnique({
      where: {
        userId_organizationId: {
          userId,
          organizationId,
        },
      },
      select: { isActive: true },
    });

    return admin?.isActive === true;
  } catch (error) {
    console.error('Error checking organization admin status:', error);
    return false;
  }
}

/**
 * Check if a user has admin privileges in an organization (either owner or admin)
 */
export async function hasOrganizationAdminPrivileges(userId: number, organizationId: number): Promise<boolean> {
  try {
    const [isOwner, isAdmin] = await Promise.all([
      isOrganizationOwner(userId, organizationId),
      isOrganizationAdmin(userId, organizationId),
    ]);

    return isOwner || isAdmin;
  } catch (error) {
    console.error('Error checking organization admin privileges:', error);
    return false;
  }
}

/**
 * Get all organizations where a user has admin privileges
 */
export async function getUserOrganizationAdminPrivileges(userId: number): Promise<{
  ownedOrganizations: number[];
  adminOrganizations: number[];
  allAdminOrganizations: number[];
}> {
  try {
    const [ownedOrgs, adminOrgs] = await Promise.all([
      // Organizations owned by the user
      prisma.organization.findMany({
        where: { ownerUserId: userId },
        select: { id: true },
      }),
      // Organizations where user is admin
      prisma.organizationAdmin.findMany({
        where: {
          userId,
          isActive: true,
        },
        select: { organizationId: true },
      }),
    ]);

    const ownedOrganizations = ownedOrgs.map(org => org.id);
    const adminOrganizations = adminOrgs.map(admin => admin.organizationId);
    const allAdminOrganizations = [...new Set([...ownedOrganizations, ...adminOrganizations])];

    return {
      ownedOrganizations,
      adminOrganizations,
      allAdminOrganizations,
    };
  } catch (error) {
    console.error('Error getting user organization admin privileges:', error);
    return {
      ownedOrganizations: [],
      adminOrganizations: [],
      allAdminOrganizations: [],
    };
  }
}

/**
 * Check if a user can access a department (based on organization admin privileges)
 */
export async function canAccessDepartment(userId: number, departmentId: number): Promise<boolean> {
  try {
    const department = await prisma.department.findUnique({
      where: { id: departmentId },
      select: { organizationId: true },
    });

    if (!department) {
      return false;
    }

    return await hasOrganizationAdminPrivileges(userId, department.organizationId);
  } catch (error) {
    console.error('Error checking department access:', error);
    return false;
  }
}

/**
 * Check if a user can access a task (based on organization admin privileges or task assignment)
 */
export async function canAccessTask(userId: number, taskId: number): Promise<boolean> {
  try {
    const task = await prisma.task.findUnique({
      where: { id: taskId },
      select: { 
        organizationId: true,
        createdByUserId: true,
        taskAssignments: {
          where: { userId },
          select: { id: true },
        },
      },
    });

    if (!task) {
      return false;
    }

    // Check if user created the task
    if (task.createdByUserId === userId) {
      return true;
    }

    // Check if user is assigned to the task
    if (task.taskAssignments.length > 0) {
      return true;
    }

    // Check if user has organization admin privileges
    return await hasOrganizationAdminPrivileges(userId, task.organizationId);
  } catch (error) {
    console.error('Error checking task access:', error);
    return false;
  }
}

/**
 * Filter organizations based on user's admin privileges
 */
export async function filterOrganizationsByAdminPrivileges(
  userId: number,
  organizationIds: number[]
): Promise<number[]> {
  try {
    const privileges = await getUserOrganizationAdminPrivileges(userId);
    return organizationIds.filter(orgId => privileges.allAdminOrganizations.includes(orgId));
  } catch (error) {
    console.error('Error filtering organizations by admin privileges:', error);
    return [];
  }
}

/**
 * Check if a user has global admin role (isAdmin = true in UserRole)
 */
export async function hasGlobalAdminRole(userId: number): Promise<boolean> {
  try {
    const user = await prisma.user.findUnique({
      where: { id: userId },
      include: { userRole: true },
    });

    return user?.userRole?.isAdmin === true;
  } catch (error) {
    console.error('Error checking global admin role:', error);
    return false;
  }
}

/**
 * Check if a user has owner role (isOwner = true in UserRole)
 */
export async function hasOwnerRole(userId: number): Promise<boolean> {
  try {
    const user = await prisma.user.findUnique({
      where: { id: userId },
      include: { userRole: true },
    });

    return user?.userRole?.isOwner === true;
  } catch (error) {
    console.error('Error checking owner role:', error);
    return false;
  }
}

/**
 * Check if a user has bot role (isBot = true in UserRole)
 */
export async function hasBotRole(userId: number): Promise<boolean> {
  try {
    const user = await prisma.user.findUnique({
      where: { id: userId },
      include: { userRole: true },
    });

    return user?.userRole?.isBot === true;
  } catch (error) {
    console.error('Error checking bot role:', error);
    return false;
  }
}
