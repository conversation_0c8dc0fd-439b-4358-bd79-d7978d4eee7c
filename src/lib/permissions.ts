import { prisma } from '@/lib/prisma';

/**
 * Check if a user is an owner of an organization
 */
export async function isOrganizationOwner(userId: number, organizationId: number): Promise<boolean> {
  try {
    const organization = await prisma.organization.findUnique({
      where: { id: organizationId },
      select: { ownerUserId: true },
    });

    return organization?.ownerUserId === userId;
  } catch (error) {
    console.error('Error checking organization ownership:', error);
    return false;
  }
}

/**
 * Check if a user is an admin of an organization
 */
export async function isOrganizationAdmin(userId: number, organizationId: number): Promise<boolean> {
  try {
    const admin = await prisma.organizationAdmin.findUnique({
      where: {
        userId_organizationId: {
          userId,
          organizationId,
        },
      },
      select: { isActive: true },
    });

    return admin?.isActive === true;
  } catch (error) {
    console.error('Error checking organization admin status:', error);
    return false;
  }
}

/**
 * Check if a user has admin privileges in an organization (either owner or admin)
 */
export async function hasOrganizationAdminPrivileges(userId: number, organizationId: number): Promise<boolean> {
  try {
    const [isOwner, isAdmin] = await Promise.all([
      isOrganizationOwner(userId, organizationId),
      isOrganizationAdmin(userId, organizationId),
    ]);

    return isOwner || isAdmin;
  } catch (error) {
    console.error('Error checking organization admin privileges:', error);
    return false;
  }
}

/**
 * Get all organizations where a user has admin privileges
 */
export async function getUserOrganizationAdminPrivileges(userId: number): Promise<{
  ownedOrganizations: number[];
  adminOrganizations: number[];
  allAdminOrganizations: number[];
}> {
  try {
    const [ownedOrgs, adminOrgs] = await Promise.all([
      // Organizations owned by the user
      prisma.organization.findMany({
        where: { ownerUserId: userId },
        select: { id: true },
      }),
      // Organizations where user is admin
      prisma.organizationAdmin.findMany({
        where: {
          userId,
          isActive: true,
        },
        select: { organizationId: true },
      }),
    ]);

    const ownedOrganizations = ownedOrgs.map(org => org.id);
    const adminOrganizations = adminOrgs.map(admin => admin.organizationId);
    const allAdminOrganizations = [...new Set([...ownedOrganizations, ...adminOrganizations])];

    return {
      ownedOrganizations,
      adminOrganizations,
      allAdminOrganizations,
    };
  } catch (error) {
    console.error('Error getting user organization admin privileges:', error);
    return {
      ownedOrganizations: [],
      adminOrganizations: [],
      allAdminOrganizations: [],
    };
  }
}

/**
 * Check if a user can access a department (based on organization admin privileges)
 */
export async function canAccessDepartment(userId: number, departmentId: number): Promise<boolean> {
  try {
    const department = await prisma.department.findUnique({
      where: { id: departmentId },
      select: { organizationId: true },
    });

    if (!department) {
      return false;
    }

    return await hasOrganizationAdminPrivileges(userId, department.organizationId);
  } catch (error) {
    console.error('Error checking department access:', error);
    return false;
  }
}

/**
 * Check if a user can access a task (based on organization admin privileges or task assignment)
 */
export async function canAccessTask(userId: number, taskId: number): Promise<boolean> {
  try {
    const task = await prisma.task.findUnique({
      where: { id: taskId },
      select: { 
        organizationId: true,
        createdByUserId: true,
        taskAssignments: {
          where: { userId },
          select: { id: true },
        },
      },
    });

    if (!task) {
      return false;
    }

    // Check if user created the task
    if (task.createdByUserId === userId) {
      return true;
    }

    // Check if user is assigned to the task
    if (task.taskAssignments.length > 0) {
      return true;
    }

    // Check if user has organization admin privileges
    return await hasOrganizationAdminPrivileges(userId, task.organizationId);
  } catch (error) {
    console.error('Error checking task access:', error);
    return false;
  }
}

/**
 * Filter organizations based on user's admin privileges
 */
export async function filterOrganizationsByAdminPrivileges(
  userId: number,
  organizationIds: number[]
): Promise<number[]> {
  try {
    const privileges = await getUserOrganizationAdminPrivileges(userId);
    return organizationIds.filter(orgId => privileges.allAdminOrganizations.includes(orgId));
  } catch (error) {
    console.error('Error filtering organizations by admin privileges:', error);
    return [];
  }
}

/**
 * Check if a user has global admin role (isAdmin = true in UserRole)
 */
export async function hasGlobalAdminRole(userId: number): Promise<boolean> {
  try {
    const user = await prisma.user.findUnique({
      where: { id: userId },
      include: { userRole: true },
    });

    return user?.userRole?.isAdmin === true;
  } catch (error) {
    console.error('Error checking global admin role:', error);
    return false;
  }
}

/**
 * Check if a user has owner role (isOwner = true in UserRole)
 */
export async function hasOwnerRole(userId: number): Promise<boolean> {
  try {
    const user = await prisma.user.findUnique({
      where: { id: userId },
      include: { userRole: true },
    });

    return user?.userRole?.isOwner === true;
  } catch (error) {
    console.error('Error checking owner role:', error);
    return false;
  }
}

/**
 * Check if a user has bot role (isBot = true in UserRole)
 */
export async function hasBotRole(userId: number): Promise<boolean> {
  try {
    const user = await prisma.user.findUnique({
      where: { id: userId },
      include: { userRole: true },
    });

    return user?.userRole?.isBot === true;
  } catch (error) {
    console.error('Error checking bot role:', error);
    return false;
  }
}

// Feedback Permission Functions

/**
 * Check if a user is a department leader
 */
export async function isDepartmentLeader(userId: number, departmentId?: number): Promise<boolean> {
  try {
    const whereClause: any = {
      userId,
      isLeader: true,
    };

    if (departmentId) {
      whereClause.departmentId = departmentId;
    }

    const leaderMembership = await prisma.departmentMember.findFirst({
      where: whereClause,
    });

    return !!leaderMembership;
  } catch (error) {
    console.error('Error checking department leader status:', error);
    return false;
  }
}

/**
 * Get user's organization context (organization they belong to)
 */
export async function getUserOrganizationContext(userId: number): Promise<{
  organizationId: number | null;
  departmentIds: number[];
  isOwner: boolean;
  isAdmin: boolean;
  isMember: boolean;
  leaderDepartmentIds: number[];
}> {
  try {
    const user = await prisma.user.findUnique({
      where: { id: userId },
      include: {
        userRole: true,
        departmentMembers: {
          include: {
            department: {
              select: {
                id: true,
                organizationId: true,
              },
            },
          },
        },
        ownedOrganizations: {
          select: { id: true },
        },
        organizationAdmins: {
          where: { isActive: true },
          select: { organizationId: true },
        },
      },
    });

    if (!user) {
      return {
        organizationId: null,
        departmentIds: [],
        isOwner: false,
        isAdmin: false,
        isMember: false,
        leaderDepartmentIds: [],
      };
    }

    // Get organization ID from department membership or owned organizations
    let organizationId: number | null = null;
    const departmentIds: number[] = [];
    const leaderDepartmentIds: number[] = [];

    // From department memberships
    for (const membership of user.departmentMembers) {
      departmentIds.push(membership.departmentId);
      if (membership.isLeader) {
        leaderDepartmentIds.push(membership.departmentId);
      }
      if (!organizationId) {
        organizationId = membership.department.organizationId;
      }
    }

    // From owned organizations
    if (user.ownedOrganizations.length > 0 && !organizationId) {
      organizationId = user.ownedOrganizations[0].id;
    }

    // From admin organizations
    if (user.organizationAdmins.length > 0 && !organizationId) {
      organizationId = user.organizationAdmins[0].organizationId;
    }

    return {
      organizationId,
      departmentIds,
      isOwner: user.userRole.isOwner,
      isAdmin: user.userRole.isAdmin,
      isMember: user.userRole.isMember,
      leaderDepartmentIds,
    };
  } catch (error) {
    console.error('Error getting user organization context:', error);
    return {
      organizationId: null,
      departmentIds: [],
      isOwner: false,
      isAdmin: false,
      isMember: false,
      leaderDepartmentIds: [],
    };
  }
}

/**
 * Check if a user can create feedback of a specific type
 */
export async function canCreateFeedback(userId: number, feedbackType: string): Promise<boolean> {
  try {
    const userContext = await getUserOrganizationContext(userId);

    switch (feedbackType) {
      case 'private':
        // Only owner, admin, and member leaders can create private feedback
        return userContext.isOwner || userContext.isAdmin || userContext.leaderDepartmentIds.length > 0;

      case 'task':
        // Only owner, admin, and member leaders can create task feedback
        return userContext.isOwner || userContext.isAdmin || userContext.leaderDepartmentIds.length > 0;

      case 'department':
        // Only owner and admin can create department feedback
        return userContext.isOwner || userContext.isAdmin;

      case 'organization':
        // Only owner can create organization feedback
        return userContext.isOwner;

      default:
        return false;
    }
  } catch (error) {
    console.error('Error checking feedback creation permission:', error);
    return false;
  }
}

/**
 * Get eligible users for feedback assignment based on feedback type
 */
export async function getEligibleFeedbackUsers(
  userId: number,
  feedbackType: string,
  taskId?: number,
  departmentId?: number,
  organizationId?: number
): Promise<number[]> {
  try {
    const userContext = await getUserOrganizationContext(userId);

    if (!userContext.organizationId) {
      return [];
    }

    switch (feedbackType) {
      case 'private':
        if (userContext.isOwner || userContext.isAdmin) {
          // Owner/Admin can select users within their organization
          const orgUsers = await prisma.departmentMember.findMany({
            where: {
              department: {
                organizationId: userContext.organizationId,
              },
              user: {
                userRole: {
                  isMember: true,
                },
              },
            },
            select: { userId: true },
          });
          return orgUsers.map(u => u.userId);
        } else if (userContext.leaderDepartmentIds.length > 0) {
          // Member leaders can select users within their departments
          const deptUsers = await prisma.departmentMember.findMany({
            where: {
              departmentId: { in: userContext.leaderDepartmentIds },
              user: {
                userRole: {
                  isMember: true,
                },
              },
            },
            select: { userId: true },
          });
          return deptUsers.map(u => u.userId);
        }
        return [];

      case 'task':
        if (!taskId) return [];

        // Get task assigned users who are members and not leaders
        const taskUsers = await prisma.taskAssignment.findMany({
          where: {
            taskId,
            isActive: true,
            user: {
              userRole: {
                isMember: true,
              },
            },
          },
          include: {
            user: {
              include: {
                departmentMembers: {
                  select: { isLeader: true },
                },
              },
            },
          },
        });

        // Filter out leaders
        return taskUsers
          .filter(assignment => !assignment.user.departmentMembers.some(dm => dm.isLeader))
          .map(assignment => assignment.userId);

      case 'department':
        if (!departmentId) return [];

        // Get all members in the department
        const deptMembers = await prisma.departmentMember.findMany({
          where: {
            departmentId,
            user: {
              userRole: {
                isMember: true,
              },
            },
          },
          select: { userId: true },
        });
        return deptMembers.map(m => m.userId);

      case 'organization':
        // Get all members in the organization
        const orgMembers = await prisma.departmentMember.findMany({
          where: {
            department: {
              organizationId: userContext.organizationId,
            },
            user: {
              userRole: {
                isMember: true,
              },
            },
          },
          select: { userId: true },
        });
        return orgMembers.map(m => m.userId);

      default:
        return [];
    }
  } catch (error) {
    console.error('Error getting eligible feedback users:', error);
    return [];
  }
}

/**
 * Check if a user can delete a specific feedback
 */
export async function canDeleteFeedback(userId: number, feedbackId: number): Promise<boolean> {
  try {
    const feedback = await prisma.feedback.findUnique({
      where: { id: feedbackId },
      select: {
        createFromId: true,
      },
    });

    if (!feedback) {
      return false;
    }

    // Only feedback creator can delete
    if (feedback.createFromId === userId) {
      return true;
    }

    // Organization owner can also delete
    const userContext = await getUserOrganizationContext(userId);
    return userContext.isOwner;
  } catch (error) {
    console.error('Error checking feedback deletion permission:', error);
    return false;
  }
}
