import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { authenticateUser } from '@/lib/auth-utils';

/**
 * GET API for feedback types
 * 
 * This endpoint returns all available feedback types (master data).
 * No filtering or pagination is needed as this is a small master data table.
 * 
 * Authentication required: Yes
 * 
 * Response format:
 * {
 *   "feedbackTypes": [
 *     {
 *       "id": 1,
 *       "name": "private",
 *       "description": "Private feedback between individuals",
 *       "createdAt": "2025-07-31T06:56:39.770Z",
 *       "updatedAt": "2025-07-31T06:56:39.770Z"
 *     },
 *     ...
 *   ]
 * }
 */
export async function GET(request: NextRequest) {
  try {
    // Authenticate the user
    const auth = await authenticateUser(request);
    if ('error' in auth) {
      return NextResponse.json({ error: auth.error }, { status: auth.status });
    }

    // Get all feedback types
    const feedbackTypes = await prisma.feedbackType.findMany({
      orderBy: {
        id: 'asc',
      },
    });

    return NextResponse.json({ feedbackTypes });
  } catch (error) {
    console.error('Error fetching feedback types:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
