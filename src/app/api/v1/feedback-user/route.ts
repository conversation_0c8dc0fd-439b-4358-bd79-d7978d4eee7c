import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { authenticateUser } from '@/lib/auth-utils';
import socketServerService from '@/lib/socket-server';

/**
 * PATCH API for feedback user management
 * 
 * This endpoint handles accept, discard, reflection, and share operations
 * for feedback assigned to users.
 * 
 * Request body:
 * {
 *   "feedbackId": 1,
 *   "action": "accept" | "discard" | "reflect" | "share",
 *   "reflection": "string" // Required for "reflect" action
 * }
 * 
 * Authentication required: Yes
 */
export async function PATCH(request: NextRequest) {
  try {
    const auth = await authenticateUser(request);
    if ('error' in auth) {
      return NextResponse.json({ error: auth.error }, { status: auth.status });
    }

    const body = await request.json();
    const { feedbackId, action, reflection } = body;

    // Validate required fields
    if (!feedbackId || !action) {
      return NextResponse.json(
        { error: 'feedbackId and action are required' },
        { status: 400 }
      );
    }

    // Validate action
    const validActions = ['accept', 'discard', 'reflect', 'share'];
    if (!validActions.includes(action)) {
      return NextResponse.json(
        { error: 'Invalid action. Must be one of: accept, discard, reflect, share' },
        { status: 400 }
      );
    }

    // Validate reflection for reflect action
    if (action === 'reflect' && (!reflection || reflection.trim() === '')) {
      return NextResponse.json(
        { error: 'Reflection text is required for reflect action' },
        { status: 400 }
      );
    }

    // Check if feedback user relationship exists
    const feedbackUser = await prisma.feedbackUser.findUnique({
      where: {
        feedbackId_userId: {
          feedbackId,
          userId: auth.userId,
        },
      },
      include: {
        feedback: {
          include: {
            feedbackType: true,
            createFrom: {
              select: {
                id: true,
                firstName: true,
                lastName: true,
                email: true,
              },
            },
          },
        },
      },
    });

    if (!feedbackUser) {
      return NextResponse.json(
        { error: 'You are not assigned to this feedback' },
        { status: 404 }
      );
    }

    // Prepare update data based on action
    let updateData: any = {};

    switch (action) {
      case 'accept':
        updateData = {
          isAccept: true,
          isDiscard: false, // Reset discard if previously set
        };
        break;

      case 'discard':
        updateData = {
          isDiscard: true,
          isAccept: false, // Reset accept if previously set
        };
        break;

      case 'reflect':
        updateData = {
          reflection: reflection.trim(),
        };
        break;

      case 'share':
        // Toggle share status
        updateData = {
          isShare: !feedbackUser.isShare,
        };
        break;
    }

    // Update the feedback user relationship
    const updatedFeedbackUser = await prisma.feedbackUser.update({
      where: {
        feedbackId_userId: {
          feedbackId,
          userId: auth.userId,
        },
      },
      data: updateData,
      include: {
        feedback: {
          include: {
            feedbackType: true,
            createFrom: {
              select: {
                id: true,
                firstName: true,
                lastName: true,
                email: true,
                imageUrl: true,
              },
            },
            feedbackUsers: {
              include: {
                user: {
                  select: {
                    id: true,
                    firstName: true,
                    lastName: true,
                    email: true,
                    imageUrl: true,
                  },
                },
              },
            },
          },
        },
        user: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true,
            imageUrl: true,
          },
        },
      },
    });

    // Return appropriate response based on action
    let message = '';
    switch (action) {
      case 'accept':
        message = 'Feedback accepted successfully';
        break;
      case 'discard':
        message = 'Feedback discarded successfully';
        break;
      case 'reflect':
        message = 'Reflection added successfully';
        break;
      case 'share':
        message = updatedFeedbackUser.isShare 
          ? 'Feedback shared successfully' 
          : 'Feedback unshared successfully';
        break;
    }

    // Emit socket notification for feedback user update
    socketServerService.emitNotification('feedback', feedbackId, 'UPDATE');

    return NextResponse.json({
      message,
      feedbackUser: updatedFeedbackUser,
    });
  } catch (error) {
    console.error('Error updating feedback user:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

/**
 * GET API for feedback user details
 * 
 * Query parameters:
 * - feedbackId: Get feedback user relationship for specific feedback
 * 
 * Authentication required: Yes
 */
export async function GET(request: NextRequest) {
  try {
    const auth = await authenticateUser(request);
    if ('error' in auth) {
      return NextResponse.json({ error: auth.error }, { status: auth.status });
    }

    const url = new URL(request.url);
    const feedbackId = url.searchParams.get('feedbackId');

    if (!feedbackId) {
      return NextResponse.json(
        { error: 'feedbackId parameter is required' },
        { status: 400 }
      );
    }

    const feedbackIdNum = parseInt(feedbackId);
    if (isNaN(feedbackIdNum)) {
      return NextResponse.json(
        { error: 'Invalid feedbackId' },
        { status: 400 }
      );
    }

    // Get feedback user relationship
    const feedbackUser = await prisma.feedbackUser.findUnique({
      where: {
        feedbackId_userId: {
          feedbackId: feedbackIdNum,
          userId: auth.userId,
        },
      },
      include: {
        feedback: {
          include: {
            feedbackType: true,
            createFrom: {
              select: {
                id: true,
                firstName: true,
                lastName: true,
                email: true,
                imageUrl: true,
              },
            },
          },
        },
        user: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true,
            imageUrl: true,
          },
        },
      },
    });

    if (!feedbackUser) {
      return NextResponse.json(
        { error: 'You are not assigned to this feedback' },
        { status: 404 }
      );
    }

    return NextResponse.json({ feedbackUser });
  } catch (error) {
    console.error('Error fetching feedback user:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
