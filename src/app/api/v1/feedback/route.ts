import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { authenticateUser } from '@/lib/auth-utils';
import socketServerService from '@/lib/socket-server';
import {
  canCreateFeedback,
  getEligibleFeedbackUsers,
  canDeleteFeedback,
  getUserOrganizationContext
} from '@/lib/permissions';

// Standard include object for feedback queries
const feedbackInclude = {
  feedbackType: true,
  createFrom: {
    select: {
      id: true,
      firstName: true,
      lastName: true,
      email: true,
      imageUrl: true,
    },
  },
  task: {
    select: {
      id: true,
      taskTitle: true,
      taskDescription: true,
    },
  },
  department: {
    select: {
      id: true,
      name: true,
      description: true,
    },
  },
  organization: {
    select: {
      id: true,
      name: true,
      description: true,
    },
  },
  feedbackUsers: {
    include: {
      user: {
        select: {
          id: true,
          firstName: true,
          lastName: true,
          email: true,
          imageUrl: true,
        },
      },
    },
  },
};

/**
 * GET API for feedback
 * 
 * Query parameters:
 * - id: Get specific feedback by ID
 * - type: Filter by feedback type (private, task, department, organization)
 * - category: Personal feedback categories (created, assigned, shared)
 * - organizationId: Filter by organization (for admin/owner views)
 * - departmentId: Filter by department (for admin/leader views)
 * 
 * Authentication required: Yes
 */
export async function GET(request: NextRequest) {
  try {
    const auth = await authenticateUser(request);
    if ('error' in auth) {
      return NextResponse.json({ error: auth.error }, { status: auth.status });
    }

    const url = new URL(request.url);
    const id = url.searchParams.get('id');
    const type = url.searchParams.get('type');
    const category = url.searchParams.get('category');
    const organizationId = url.searchParams.get('organizationId');
    const departmentId = url.searchParams.get('departmentId');

    // Get single feedback by ID
    if (id) {
      const feedbackId = parseInt(id);
      if (isNaN(feedbackId)) {
        return NextResponse.json({ error: 'Invalid feedback ID' }, { status: 400 });
      }

      const feedback = await prisma.feedback.findUnique({
        where: { id: feedbackId },
        include: feedbackInclude,
      });

      if (!feedback) {
        return NextResponse.json({ error: 'Feedback not found' }, { status: 404 });
      }

      // Check if user has permission to view this feedback
      const isCreator = feedback.createFromId === auth.userId;
      const isAssigned = feedback.feedbackUsers.some(fu => fu.userId === auth.userId);
      const isShared = feedback.feedbackUsers.some(fu => fu.userId === auth.userId && fu.isShare);
      
      const userContext = await getUserOrganizationContext(auth.userId);
      const isAdminOrOwner = userContext.isOwner || userContext.isAdmin;

      if (!isCreator && !isAssigned && !isShared && !isAdminOrOwner) {
        return NextResponse.json({ error: 'You do not have permission to view this feedback' }, { status: 403 });
      }

      return NextResponse.json({ feedback });
    }

    // Handle personal feedback categories
    if (category) {
      let feedbacks;
      
      switch (category) {
        case 'created':
          feedbacks = await prisma.feedback.findMany({
            where: { createFromId: auth.userId },
            include: feedbackInclude,
            orderBy: { createdAt: 'desc' },
          });
          break;

        case 'assigned':
          feedbacks = await prisma.feedback.findMany({
            where: {
              feedbackUsers: {
                some: {
                  userId: auth.userId,
                },
              },
            },
            include: feedbackInclude,
            orderBy: { createdAt: 'desc' },
          });
          break;

        case 'shared':
          feedbacks = await prisma.feedback.findMany({
            where: {
              feedbackUsers: {
                some: {
                  userId: auth.userId,
                  isShare: true,
                },
              },
            },
            include: feedbackInclude,
            orderBy: { createdAt: 'desc' },
          });
          break;

        default:
          return NextResponse.json({ error: 'Invalid category' }, { status: 400 });
      }

      return NextResponse.json({ feedbacks });
    }

    // Handle administrative views (for owners/admins/leaders)
    const userContext = await getUserOrganizationContext(auth.userId);
    
    if (!userContext.isOwner && !userContext.isAdmin && userContext.leaderDepartmentIds.length === 0) {
      return NextResponse.json({ error: 'You do not have permission to view all feedback' }, { status: 403 });
    }

    let whereClause: any = {};

    // Apply organization filter
    if (organizationId) {
      const orgId = parseInt(organizationId);
      if (isNaN(orgId)) {
        return NextResponse.json({ error: 'Invalid organization ID' }, { status: 400 });
      }
      
      // Check if user has access to this organization
      if (!userContext.isOwner && userContext.organizationId !== orgId) {
        return NextResponse.json({ error: 'You do not have access to this organization' }, { status: 403 });
      }
      
      whereClause.createFrom = {
        departmentMembers: {
          some: {
            department: {
              organizationId: orgId,
            },
          },
        },
      };
    } else if (userContext.organizationId) {
      // Default to user's organization
      whereClause.createFrom = {
        departmentMembers: {
          some: {
            department: {
              organizationId: userContext.organizationId,
            },
          },
        },
      };
    }

    // Apply department filter
    if (departmentId) {
      const deptId = parseInt(departmentId);
      if (isNaN(deptId)) {
        return NextResponse.json({ error: 'Invalid department ID' }, { status: 400 });
      }
      
      // Check if user has access to this department
      if (!userContext.isOwner && !userContext.isAdmin && !userContext.leaderDepartmentIds.includes(deptId)) {
        return NextResponse.json({ error: 'You do not have access to this department' }, { status: 403 });
      }
      
      whereClause.createFrom = {
        departmentMembers: {
          some: {
            departmentId: deptId,
          },
        },
      };
    }

    // Apply type filter
    if (type) {
      const feedbackType = await prisma.feedbackType.findUnique({
        where: { name: type },
      });
      
      if (!feedbackType) {
        return NextResponse.json({ error: 'Invalid feedback type' }, { status: 400 });
      }
      
      whereClause.typeId = feedbackType.id;
    }

    const feedbacks = await prisma.feedback.findMany({
      where: whereClause,
      include: feedbackInclude,
      orderBy: { createdAt: 'desc' },
    });

    return NextResponse.json({ feedbacks });
  } catch (error) {
    console.error('Error fetching feedback:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

/**
 * POST API to create new feedback
 *
 * Request body:
 * {
 *   "typeId": 1,
 *   "situation": "string",
 *   "behavior": "string",
 *   "impact": "string",
 *   "actionable": "string",
 *   "appreciation": "string",
 *   "growthToken": "string",
 *   "userIds": [1, 2, 3]
 * }
 */
export async function POST(request: NextRequest) {
  try {
    const auth = await authenticateUser(request);
    if ('error' in auth) {
      return NextResponse.json({ error: auth.error }, { status: auth.status });
    }

    const body = await request.json();
    const {
      typeId,
      situation,
      behavior,
      impact,
      actionable,
      appreciation,
      growthToken,
      userIds = [],
      taskId,
      departmentId,
      organizationId
    } = body;

    // Validate required fields
    if (!typeId) {
      return NextResponse.json({ error: 'Feedback type is required' }, { status: 400 });
    }

    if (!Array.isArray(userIds) || userIds.length === 0) {
      return NextResponse.json({ error: 'At least one user must be assigned to the feedback' }, { status: 400 });
    }

    // Get feedback type
    const feedbackType = await prisma.feedbackType.findUnique({
      where: { id: typeId },
    });

    if (!feedbackType) {
      return NextResponse.json({ error: 'Invalid feedback type' }, { status: 400 });
    }

    // Validate required context parameters based on feedback type
    if (feedbackType.name === 'task' && !taskId) {
      return NextResponse.json({ error: 'Task ID is required for task feedback' }, { status: 400 });
    }
    if (feedbackType.name === 'department' && !departmentId) {
      return NextResponse.json({ error: 'Department ID is required for department feedback' }, { status: 400 });
    }
    if (feedbackType.name === 'organization' && !organizationId) {
      return NextResponse.json({ error: 'Organization ID is required for organization feedback' }, { status: 400 });
    }

    // Check if user can create this type of feedback
    const canCreate = await canCreateFeedback(auth.userId, feedbackType.name);
    if (!canCreate) {
      return NextResponse.json(
        { error: `You do not have permission to create ${feedbackType.name} feedback` },
        { status: 403 }
      );
    }

    // Get eligible users for this feedback type
    const eligibleUsers = await getEligibleFeedbackUsers(
      auth.userId,
      feedbackType.name,
      taskId,
      departmentId,
      organizationId
    );

    // Validate that all assigned users are eligible
    const invalidUsers = userIds.filter(userId => !eligibleUsers.includes(userId));
    if (invalidUsers.length > 0) {
      return NextResponse.json(
        { error: `Some users are not eligible for ${feedbackType.name} feedback` },
        { status: 400 }
      );
    }

    // Create feedback with users in a transaction
    const result = await prisma.$transaction(async (tx) => {
      // Create the feedback
      const feedback = await tx.feedback.create({
        data: {
          typeId,
          createFromId: auth.userId,
          taskId: taskId ? parseInt(taskId) : null,
          departmentId: departmentId ? parseInt(departmentId) : null,
          organizationId: organizationId ? parseInt(organizationId) : null,
          situation,
          behavior,
          impact,
          actionable,
          appreciation,
          growthToken,
        },
      });

      // Create feedback user relationships
      const feedbackUsers = await Promise.all(
        userIds.map(userId =>
          tx.feedbackUser.create({
            data: {
              feedbackId: feedback.id,
              userId,
            },
          })
        )
      );

      return { feedback, feedbackUsers };
    });

    // Fetch the complete feedback with relations
    const completeFeedback = await prisma.feedback.findUnique({
      where: { id: result.feedback.id },
      include: feedbackInclude,
    });

    // Emit socket notification for feedback creation
    socketServerService.emitNotification('feedback', completeFeedback.id, 'CREATE');

    return NextResponse.json({ feedback: completeFeedback }, { status: 201 });
  } catch (error) {
    console.error('Error creating feedback:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

/**
 * PATCH API to update feedback
 *
 * Request body:
 * {
 *   "id": 1,
 *   "situation": "string",
 *   "behavior": "string",
 *   "impact": "string",
 *   "actionable": "string",
 *   "appreciation": "string",
 *   "growthToken": "string",
 *   "userIds": [1, 2, 3] // Optional: to update assigned users
 * }
 */
export async function PATCH(request: NextRequest) {
  try {
    const auth = await authenticateUser(request);
    if ('error' in auth) {
      return NextResponse.json({ error: auth.error }, { status: auth.status });
    }

    const body = await request.json();
    const {
      id,
      situation,
      behavior,
      impact,
      actionable,
      appreciation,
      growthToken,
      userIds,
      taskId,
      departmentId,
      organizationId
    } = body;

    if (!id) {
      return NextResponse.json({ error: 'Feedback ID is required' }, { status: 400 });
    }

    // Get existing feedback
    const existingFeedback = await prisma.feedback.findUnique({
      where: { id },
      include: {
        feedbackType: true,
        feedbackUsers: true,
      },
    });

    if (!existingFeedback) {
      return NextResponse.json({ error: 'Feedback not found' }, { status: 404 });
    }

    // Check if user can update this feedback (only creator can update)
    if (existingFeedback.createFromId !== auth.userId) {
      return NextResponse.json(
        { error: 'You can only update feedback you created' },
        { status: 403 }
      );
    }

    // Prepare update data
    const updateData: any = {};
    if (situation !== undefined) updateData.situation = situation;
    if (behavior !== undefined) updateData.behavior = behavior;
    if (impact !== undefined) updateData.impact = impact;
    if (actionable !== undefined) updateData.actionable = actionable;
    if (appreciation !== undefined) updateData.appreciation = appreciation;
    if (growthToken !== undefined) updateData.growthToken = growthToken;
    if (taskId !== undefined) updateData.taskId = taskId ? parseInt(taskId) : null;
    if (departmentId !== undefined) updateData.departmentId = departmentId ? parseInt(departmentId) : null;
    if (organizationId !== undefined) updateData.organizationId = organizationId ? parseInt(organizationId) : null;

    // Update feedback and users in a transaction
    const result = await prisma.$transaction(async (tx) => {
      // Update the feedback
      const feedback = await tx.feedback.update({
        where: { id },
        data: updateData,
      });

      // Update users if provided
      if (userIds && Array.isArray(userIds)) {
        // Validate eligible users
        const eligibleUsers = await getEligibleFeedbackUsers(
          auth.userId,
          existingFeedback.feedbackType.name,
          taskId || existingFeedback.taskId,
          departmentId || existingFeedback.departmentId,
          organizationId || existingFeedback.organizationId
        );
        const invalidUsers = userIds.filter(userId => !eligibleUsers.includes(userId));

        if (invalidUsers.length > 0) {
          throw new Error(`Some users are not eligible for ${existingFeedback.feedbackType.name} feedback`);
        }

        // Delete existing user assignments
        await tx.feedbackUser.deleteMany({
          where: { feedbackId: id },
        });

        // Create new user assignments
        if (userIds.length > 0) {
          await Promise.all(
            userIds.map(userId =>
              tx.feedbackUser.create({
                data: {
                  feedbackId: id,
                  userId,
                },
              })
            )
          );
        }
      }

      return feedback;
    });

    // Fetch the complete updated feedback
    const updatedFeedback = await prisma.feedback.findUnique({
      where: { id },
      include: feedbackInclude,
    });

    // Emit socket notification for feedback update
    socketServerService.emitNotification('feedback', updatedFeedback.id, 'UPDATE');

    return NextResponse.json({ feedback: updatedFeedback });
  } catch (error) {
    console.error('Error updating feedback:', error);
    if (error instanceof Error && error.message.includes('not eligible')) {
      return NextResponse.json({ error: error.message }, { status: 400 });
    }
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

/**
 * DELETE API to delete feedback
 *
 * Query parameters:
 * - id: Feedback ID to delete
 */
export async function DELETE(request: NextRequest) {
  try {
    const auth = await authenticateUser(request);
    if ('error' in auth) {
      return NextResponse.json({ error: auth.error }, { status: auth.status });
    }

    const url = new URL(request.url);
    const id = url.searchParams.get('id');

    if (!id) {
      return NextResponse.json({ error: 'Feedback ID is required' }, { status: 400 });
    }

    const feedbackId = parseInt(id);
    if (isNaN(feedbackId)) {
      return NextResponse.json({ error: 'Invalid feedback ID' }, { status: 400 });
    }

    // Check if user can delete this feedback
    const canDelete = await canDeleteFeedback(auth.userId, feedbackId);
    if (!canDelete) {
      return NextResponse.json(
        { error: 'You do not have permission to delete this feedback' },
        { status: 403 }
      );
    }

    // Delete feedback (cascade will handle feedback_users)
    await prisma.feedback.delete({
      where: { id: feedbackId },
    });

    // Emit socket notification for feedback deletion
    socketServerService.emitNotification('feedback', feedbackId, 'DELETE');

    return NextResponse.json({ message: 'Feedback deleted successfully' });
  } catch (error) {
    console.error('Error deleting feedback:', error);
    if (error.code === 'P2025') {
      return NextResponse.json({ error: 'Feedback not found' }, { status: 404 });
    }
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
